"use client"

import { useState } from "react";
import { ChevronDown, ChevronUp, AlertTriangle, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import type { VideoTask } from "@/types/video-task";

interface ErrorDetailsProps {
    video: VideoTask;
    className?: string;
}

/**
 * ErrorDetails Component - 显示错误详情信息
 * 支持展开/收起详细错误信息
 */
export const ErrorDetails = ({ video, className }: ErrorDetailsProps) => {
    const [isExpanded, setIsExpanded] = useState(false);
    
    if (!video.error_log) {
        return null;
    }

    const { error_log } = video;
    
    // 获取用户友好的错误信息
    const getUserFriendlyMessage = () => {
        if (!error_log.details?.detail?.[0]) {
            return error_log.message || "An unknown error occurred";
        }
        
        const detail = error_log.details.detail[0];
        
        // 根据错误类型返回用户友好的信息
        switch (detail.type) {
            case "content_policy_violation":
                return "Content policy violation: The prompt or image contains content that is not allowed by our content policy.";
            case "invalid_input":
                return "Invalid input: Please check your prompt and image settings.";
            case "quota_exceeded":
                return "Quota exceeded: You have reached your usage limit.";
            case "model_error":
                return "Model error: There was an issue with the AI model processing.";
            default:
                return detail.msg || error_log.message || "An error occurred during generation";
        }
    };

    // 获取错误代码显示
    const getErrorCode = () => {
        return error_log.code || "UNKNOWN_ERROR";
    };

    // 获取详细错误信息
    const getDetailedError = () => {
        if (error_log.details?.detail?.[0]) {
            const detail = error_log.details.detail[0];
            return {
                type: detail.type,
                message: detail.msg,
                location: detail.loc?.join('.'),
                url: detail.url,
                input: detail.input
            };
        }
        return null;
    };

    const detailedError = getDetailedError();

    return (
        <div className={cn("space-y-2", className)}>
            {/* 简要错误信息 */}
            <div className="flex items-start gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-md">
                <AlertTriangle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                    <div className="text-sm text-red-700 dark:text-red-300 font-medium">
                        {getUserFriendlyMessage()}
                    </div>
                    <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                        Error Code: {getErrorCode()}
                    </div>
                </div>
                {detailedError && (
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                    >
                        Details
                        {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                    </button>
                )}
            </div>

            {/* 详细错误信息 */}
            {isExpanded && detailedError && (
                <div className="p-3 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-md">
                    <div className="flex items-center gap-2 mb-2">
                        <Info size={14} className="text-gray-500" />
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                            Technical Details
                        </span>
                    </div>
                    
                    <div className="space-y-2 text-xs">
                        {detailedError.type && (
                            <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Type:</span>
                                <span className="ml-2 font-mono text-gray-800 dark:text-gray-200">
                                    {detailedError.type}
                                </span>
                            </div>
                        )}
                        
                        {detailedError.message && (
                            <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Message:</span>
                                <span className="ml-2 text-gray-800 dark:text-gray-200">
                                    {detailedError.message}
                                </span>
                            </div>
                        )}
                        
                        {detailedError.location && (
                            <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Location:</span>
                                <span className="ml-2 font-mono text-gray-800 dark:text-gray-200">
                                    {detailedError.location}
                                </span>
                            </div>
                        )}
                        
                        {detailedError.url && (
                            <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Reference:</span>
                                <a 
                                    href={detailedError.url} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="ml-2 text-blue-600 dark:text-blue-400 hover:underline"
                                >
                                    View Documentation
                                </a>
                            </div>
                        )}
                        
                        {detailedError.input && (
                            <div>
                                <span className="font-medium text-gray-600 dark:text-gray-400">Input:</span>
                                <div className="mt-1 p-2 bg-gray-100 dark:bg-gray-800 rounded border">
                                    <pre className="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap break-all">
                                        {JSON.stringify(detailedError.input, null, 2)}
                                    </pre>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};
